"""
Configuration Settings

Manages all configuration settings for the Arien AI system.
"""

import os
from pathlib import Path
from typing import Optional, Dict, Any
from pydantic_settings import BaseSettings
from pydantic import Field, validator
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class Settings(BaseSettings):
    """
    Application settings with environment variable support.
    
    All settings can be overridden via environment variables with ARIEN_ prefix.
    """
    
    # Application settings
    app_name: str = Field(default="Arien AI", env="ARIEN_APP_NAME")
    version: str = Field(default="1.0.0", env="ARIEN_VERSION")
    debug: bool = Field(default=False, env="ARIEN_DEBUG")
    verbose: bool = Field(default=False, env="ARIEN_VERBOSE")
    
    # LLM Provider Settings
    # DeepSeek
    deepseek_api_key: Optional[str] = Field(default=None, env="DEEPSEEK_API_KEY")
    deepseek_base_url: str = Field(default="https://api.deepseek.com", env="DEEPSEEK_BASE_URL")
    deepseek_default_model: str = Field(default="deepseek-chat", env="DEEPSEEK_DEFAULT_MODEL")
    deepseek_max_tokens: int = Field(default=4096, env="DEEPSEEK_MAX_TOKENS")
    deepseek_temperature: float = Field(default=0.7, env="DEEPSEEK_TEMPERATURE")
    
    # Ollama
    ollama_host: str = Field(default="http://localhost:11434", env="OLLAMA_HOST")
    ollama_default_model: str = Field(default="llama3.2", env="OLLAMA_DEFAULT_MODEL")
    ollama_timeout: int = Field(default=120, env="OLLAMA_TIMEOUT")
    
    # Tool Settings
    tools_enabled: bool = Field(default=True, env="ARIEN_TOOLS_ENABLED")
    tools_timeout: int = Field(default=30, env="ARIEN_TOOLS_TIMEOUT")
    tools_max_output_length: int = Field(default=5000, env="ARIEN_TOOLS_MAX_OUTPUT_LENGTH")
    
    # File Tool Settings
    file_tools_enabled: bool = Field(default=True, env="ARIEN_FILE_TOOLS_ENABLED")
    file_tools_max_file_size: int = Field(default=10 * 1024 * 1024, env="ARIEN_FILE_TOOLS_MAX_SIZE")  # 10MB
    file_tools_allowed_extensions: str = Field(
        default=".txt,.py,.js,.html,.css,.json,.xml,.yaml,.yml,.md,.rst,.log",
        env="ARIEN_FILE_TOOLS_ALLOWED_EXTENSIONS"
    )
    file_tools_base_path: Optional[str] = Field(default=None, env="ARIEN_FILE_TOOLS_BASE_PATH")
    
    # Web Tool Settings
    web_tools_enabled: bool = Field(default=True, env="ARIEN_WEB_TOOLS_ENABLED")
    web_tools_timeout: int = Field(default=30, env="ARIEN_WEB_TOOLS_TIMEOUT")
    web_tools_max_content_length: int = Field(default=100000, env="ARIEN_WEB_TOOLS_MAX_CONTENT_LENGTH")
    web_tools_user_agent: str = Field(
        default="Arien-AI/1.0 (Agentic CLI Terminal System)",
        env="ARIEN_WEB_TOOLS_USER_AGENT"
    )
    
    # System Tool Settings
    system_tools_enabled: bool = Field(default=True, env="ARIEN_SYSTEM_TOOLS_ENABLED")
    system_tools_allow_dangerous: bool = Field(default=False, env="ARIEN_SYSTEM_TOOLS_ALLOW_DANGEROUS")
    system_tools_timeout: int = Field(default=60, env="ARIEN_SYSTEM_TOOLS_TIMEOUT")
    
    # Code Tool Settings
    code_tools_enabled: bool = Field(default=True, env="ARIEN_CODE_TOOLS_ENABLED")
    code_tools_max_file_size: int = Field(default=1024 * 1024, env="ARIEN_CODE_TOOLS_MAX_SIZE")  # 1MB
    code_tools_supported_languages: str = Field(
        default="python,javascript,typescript,java,cpp,c,rust,go,php,ruby,swift,kotlin",
        env="ARIEN_CODE_TOOLS_SUPPORTED_LANGUAGES"
    )
    
    # Retry Settings
    retry_max_attempts: int = Field(default=3, env="ARIEN_RETRY_MAX_ATTEMPTS")
    retry_base_delay: float = Field(default=1.0, env="ARIEN_RETRY_BASE_DELAY")
    retry_max_delay: float = Field(default=60.0, env="ARIEN_RETRY_MAX_DELAY")
    retry_exponential_base: float = Field(default=2.0, env="ARIEN_RETRY_EXPONENTIAL_BASE")
    
    # Logging Settings
    log_level: str = Field(default="INFO", env="ARIEN_LOG_LEVEL")
    log_file: Optional[str] = Field(default=None, env="ARIEN_LOG_FILE")
    log_format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        env="ARIEN_LOG_FORMAT"
    )
    log_max_size: int = Field(default=10 * 1024 * 1024, env="ARIEN_LOG_MAX_SIZE")  # 10MB
    log_backup_count: int = Field(default=5, env="ARIEN_LOG_BACKUP_COUNT")
    
    # Storage Settings
    data_dir: str = Field(default="~/.arien-ai", env="ARIEN_DATA_DIR")
    cache_dir: str = Field(default="~/.arien-ai/cache", env="ARIEN_CACHE_DIR")
    config_file: str = Field(default="~/.arien-ai/config.json", env="ARIEN_CONFIG_FILE")
    
    # Security Settings
    api_key_encryption: bool = Field(default=True, env="ARIEN_API_KEY_ENCRYPTION")
    secure_mode: bool = Field(default=False, env="ARIEN_SECURE_MODE")
    
    # Performance Settings
    max_concurrent_tools: int = Field(default=5, env="ARIEN_MAX_CONCURRENT_TOOLS")
    memory_limit_mb: int = Field(default=1024, env="ARIEN_MEMORY_LIMIT_MB")
    
    class Config:
        env_prefix = "ARIEN_"
        case_sensitive = False
        
    @validator("data_dir", "cache_dir", "config_file")
    def expand_paths(cls, v):
        """Expand user paths like ~/ to full paths."""
        return str(Path(v).expanduser())
    
    @validator("file_tools_allowed_extensions")
    def parse_extensions(cls, v):
        """Parse comma-separated extensions into a list."""
        if isinstance(v, str):
            return [ext.strip() for ext in v.split(",")]
        return v
    
    @validator("code_tools_supported_languages")
    def parse_languages(cls, v):
        """Parse comma-separated languages into a list."""
        if isinstance(v, str):
            return [lang.strip() for lang in v.split(",")]
        return v
    
    @validator("log_level")
    def validate_log_level(cls, v):
        """Validate log level."""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Invalid log level. Must be one of: {valid_levels}")
        return v.upper()
    
    def get_data_dir(self) -> Path:
        """Get data directory as Path object."""
        path = Path(self.data_dir)
        path.mkdir(parents=True, exist_ok=True)
        return path
    
    def get_cache_dir(self) -> Path:
        """Get cache directory as Path object."""
        path = Path(self.cache_dir)
        path.mkdir(parents=True, exist_ok=True)
        return path
    
    def get_config_file(self) -> Path:
        """Get config file as Path object."""
        return Path(self.config_file)
    
    def is_tool_enabled(self, tool_type: str) -> bool:
        """Check if a specific tool type is enabled."""
        tool_settings = {
            "file": self.file_tools_enabled,
            "web": self.web_tools_enabled,
            "system": self.system_tools_enabled,
            "code": self.code_tools_enabled
        }
        return self.tools_enabled and tool_settings.get(tool_type, False)
    
    def get_provider_config(self, provider: str) -> Dict[str, Any]:
        """Get configuration for a specific LLM provider."""
        if provider.lower() == "deepseek":
            return {
                "api_key": self.deepseek_api_key,
                "base_url": self.deepseek_base_url,
                "default_model": self.deepseek_default_model,
                "max_tokens": self.deepseek_max_tokens,
                "temperature": self.deepseek_temperature
            }
        elif provider.lower() == "ollama":
            return {
                "host": self.ollama_host,
                "default_model": self.ollama_default_model,
                "timeout": self.ollama_timeout
            }
        else:
            raise ValueError(f"Unknown provider: {provider}")
    
    def save_to_file(self, file_path: Optional[str] = None):
        """Save current settings to a JSON file."""
        import json
        
        file_path = file_path or self.config_file
        config_data = self.dict()
        
        # Remove sensitive data
        sensitive_keys = ["deepseek_api_key"]
        for key in sensitive_keys:
            if key in config_data:
                config_data[key] = "***REDACTED***"
        
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)
        
        with open(file_path, 'w') as f:
            json.dump(config_data, f, indent=2)
    
    @classmethod
    def load_from_file(cls, file_path: str) -> "Settings":
        """Load settings from a JSON file."""
        import json
        
        if not Path(file_path).exists():
            return cls()
        
        with open(file_path, 'r') as f:
            config_data = json.load(f)
        
        return cls(**config_data)


# Global settings instance
_settings: Optional[Settings] = None


def get_settings() -> Settings:
    """Get the global settings instance."""
    global _settings
    if _settings is None:
        _settings = Settings()
    return _settings


def reload_settings():
    """Reload settings from environment and config file."""
    global _settings
    _settings = None
    return get_settings()
