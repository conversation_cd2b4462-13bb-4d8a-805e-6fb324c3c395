#!/usr/bin/env python3
"""
Arien AI - Main CLI Application

A powerful local agentic CLI terminal system with LLM function calling capabilities.
"""

import asyncio
import sys
from pathlib import Path
from typing import Optional

import typer
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.prompt import Prompt, Confirm
from rich.table import Table
from rich.live import Live
from rich.spinner import Spinner

from .core.agent import ArienAgent
from .config.settings import get_settings
from .utils.logger import setup_logger, get_logger

# Initialize Rich console
console = Console()
app = typer.Typer(
    name="arien-ai",
    help="🤖 Arien AI - Powerful Local Agentic CLI Terminal System",
    rich_markup_mode="rich",
    no_args_is_help=True,
)

# Global agent instance
agent: Optional[ArienAgent] = None


def display_banner():
    """Display the Arien AI banner."""
    banner_text = Text()
    banner_text.append("🤖 ", style="bold blue")
    banner_text.append("ARIEN AI", style="bold cyan")
    banner_text.append(" - Agentic CLI Terminal System", style="bold white")
    
    panel = Panel(
        banner_text,
        border_style="cyan",
        padding=(1, 2),
        title="[bold blue]Welcome[/bold blue]",
        title_align="center",
    )
    console.print(panel)


@app.command()
def chat(
    message: Optional[str] = typer.Argument(None, help="Initial message to send to the agent"),
    provider: str = typer.Option("deepseek", "--provider", "-p", help="LLM provider (deepseek/ollama)"),
    model: str = typer.Option("deepseek-chat", "--model", "-m", help="Model to use"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Enable verbose output"),
    max_retries: int = typer.Option(3, "--max-retries", help="Maximum retry attempts"),
):
    """💬 Start an interactive chat session with the AI agent."""
    global agent
    
    # Setup logging
    setup_logger(verbose=verbose)
    logger = get_logger(__name__)
    
    try:
        # Initialize agent
        with console.status("[bold green]Initializing Arien AI...", spinner="dots"):
            agent = ArienAgent(
                provider=provider,
                model=model,
                max_retries=max_retries,
                verbose=verbose
            )
        
        display_banner()
        
        # Show configuration
        config_table = Table(title="Configuration", show_header=True, header_style="bold magenta")
        config_table.add_column("Setting", style="cyan")
        config_table.add_column("Value", style="green")
        config_table.add_row("Provider", provider)
        config_table.add_row("Model", model)
        config_table.add_row("Max Retries", str(max_retries))
        config_table.add_row("Verbose", str(verbose))
        console.print(config_table)
        console.print()
        
        # Handle initial message if provided
        if message:
            console.print(f"[bold blue]You:[/bold blue] {message}")
            asyncio.run(process_message(message))
        
        # Interactive chat loop
        console.print("[dim]Type 'exit', 'quit', or 'bye' to end the session.[/dim]")
        console.print("[dim]Type 'help' for available commands.[/dim]")
        console.print()
        
        while True:
            try:
                user_input = Prompt.ask("[bold blue]You")
                
                if user_input.lower() in ["exit", "quit", "bye"]:
                    console.print("[bold green]Goodbye! 👋[/bold green]")
                    break
                elif user_input.lower() == "help":
                    show_help()
                elif user_input.lower() == "clear":
                    console.clear()
                    display_banner()
                elif user_input.lower() == "status":
                    show_status()
                elif user_input.strip():
                    await process_message(user_input)
                    
            except KeyboardInterrupt:
                if Confirm.ask("\n[yellow]Do you want to exit?[/yellow]"):
                    console.print("[bold green]Goodbye! 👋[/bold green]")
                    break
                else:
                    console.print("[dim]Continuing...[/dim]")
                    
    except Exception as e:
        logger.error(f"Error in chat session: {e}")
        console.print(f"[bold red]Error:[/bold red] {e}")
        sys.exit(1)


async def process_message(message: str):
    """Process a user message through the agent."""
    global agent
    
    if not agent:
        console.print("[bold red]Error:[/bold red] Agent not initialized")
        return
    
    try:
        with Live(Spinner("dots", text="[bold green]Thinking..."), console=console, refresh_per_second=10):
            response = await agent.process_message(message)
        
        # Display agent response
        console.print(f"[bold green]Arien AI:[/bold green] {response}")
        console.print()
        
    except Exception as e:
        console.print(f"[bold red]Error processing message:[/bold red] {e}")


def show_help():
    """Show available commands."""
    help_table = Table(title="Available Commands", show_header=True, header_style="bold magenta")
    help_table.add_column("Command", style="cyan")
    help_table.add_column("Description", style="white")
    
    help_table.add_row("help", "Show this help message")
    help_table.add_row("clear", "Clear the screen")
    help_table.add_row("status", "Show agent status")
    help_table.add_row("exit/quit/bye", "Exit the chat session")
    
    console.print(help_table)
    console.print()


def show_status():
    """Show agent status."""
    global agent
    
    if not agent:
        console.print("[bold red]Agent not initialized[/bold red]")
        return
    
    status = agent.get_status()
    status_table = Table(title="Agent Status", show_header=True, header_style="bold magenta")
    status_table.add_column("Metric", style="cyan")
    status_table.add_column("Value", style="green")
    
    for key, value in status.items():
        status_table.add_row(key, str(value))
    
    console.print(status_table)
    console.print()


@app.command()
def config(
    show: bool = typer.Option(False, "--show", help="Show current configuration"),
    set_key: Optional[str] = typer.Option(None, "--set", help="Set configuration key"),
    value: Optional[str] = typer.Option(None, "--value", help="Configuration value"),
):
    """⚙️ Manage configuration settings."""
    settings = get_settings()
    
    if show:
        config_table = Table(title="Current Configuration", show_header=True, header_style="bold magenta")
        config_table.add_column("Setting", style="cyan")
        config_table.add_column("Value", style="green")
        
        for key, val in settings.dict().items():
            config_table.add_row(key, str(val))
        
        console.print(config_table)
    
    elif set_key and value:
        # Set configuration value
        try:
            setattr(settings, set_key, value)
            console.print(f"[bold green]Set {set_key} = {value}[/bold green]")
        except Exception as e:
            console.print(f"[bold red]Error setting configuration:[/bold red] {e}")
    
    else:
        console.print("[yellow]Use --show to view configuration or --set with --value to modify[/yellow]")


@app.command()
def version():
    """📋 Show version information."""
    from . import __version__, __author__
    
    version_table = Table(title="Version Information", show_header=True, header_style="bold magenta")
    version_table.add_column("Component", style="cyan")
    version_table.add_column("Value", style="green")
    
    version_table.add_row("Arien AI", __version__)
    version_table.add_row("Author", __author__)
    version_table.add_row("Python", f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    
    console.print(version_table)


if __name__ == "__main__":
    app()
