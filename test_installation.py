#!/usr/bin/env python3
"""
Test script to verify Arien AI installation and basic functionality.
"""

import sys
import asyncio
from pathlib import Path

# Add the current directory to Python path for testing
sys.path.insert(0, str(Path(__file__).parent))

try:
    from arien_ai.config.settings import get_settings
    from arien_ai.config.system_prompt import get_system_prompt
    from arien_ai.utils.logger import setup_logger, get_logger
    from arien_ai.tools.file_tools import FileTools
    from arien_ai.tools.web_tools import WebTools
    from arien_ai.tools.system_tools import SystemTools
    from arien_ai.tools.code_tools import CodeTools
    from arien_ai.core.tool_executor import ToolExecutor
    from arien_ai.core.retry_logic import RetryManager
    print("✓ All imports successful")
except ImportError as e:
    print(f"✗ Import error: {e}")
    sys.exit(1)


async def test_basic_functionality():
    """Test basic functionality of core components."""
    print("\n🧪 Testing Basic Functionality")
    
    # Test settings
    try:
        settings = get_settings()
        print(f"✓ Settings loaded: {settings.app_name}")
    except Exception as e:
        print(f"✗ Settings error: {e}")
        return False
    
    # Test system prompt
    try:
        prompt = get_system_prompt()
        prompt_text = prompt.get_full_prompt()
        print(f"✓ System prompt loaded: {len(prompt_text)} characters")
    except Exception as e:
        print(f"✗ System prompt error: {e}")
        return False
    
    # Test logger
    try:
        setup_logger(verbose=True)
        logger = get_logger(__name__)
        logger.info("Test log message")
        print("✓ Logger working")
    except Exception as e:
        print(f"✗ Logger error: {e}")
        return False
    
    # Test tools
    try:
        file_tools = FileTools(verbose=True)
        web_tools = WebTools(verbose=True)
        system_tools = SystemTools(verbose=True)
        code_tools = CodeTools(verbose=True)
        print("✓ All tools initialized")
    except Exception as e:
        print(f"✗ Tool initialization error: {e}")
        return False
    
    # Test tool executor
    try:
        executor = ToolExecutor(verbose=True)
        available_tools = executor.get_available_tools()
        print(f"✓ Tool executor working: {len(available_tools)} tools available")
    except Exception as e:
        print(f"✗ Tool executor error: {e}")
        return False
    
    # Test retry manager
    try:
        retry_manager = RetryManager(max_retries=2, verbose=True)
        
        async def test_function():
            return "success"
        
        result = await retry_manager.execute_with_retry(test_function)
        print(f"✓ Retry manager working: {result}")
    except Exception as e:
        print(f"✗ Retry manager error: {e}")
        return False
    
    return True


async def test_file_operations():
    """Test file operations."""
    print("\n📁 Testing File Operations")
    
    try:
        file_tools = FileTools(verbose=True)
        
        # Test file info on current directory
        result = await file_tools.get_file_info(".")
        if result.success:
            print(f"✓ Directory info retrieved: {result.data['type']}")
        else:
            print(f"✗ Directory info failed: {result.error}")
            return False
        
        # Test listing directory
        result = await file_tools.list_directory(".", pattern="*.py")
        if result.success:
            print(f"✓ Directory listing: {len(result.data)} Python files found")
        else:
            print(f"✗ Directory listing failed: {result.error}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ File operations error: {e}")
        return False


async def test_code_analysis():
    """Test code analysis functionality."""
    print("\n🐍 Testing Code Analysis")
    
    try:
        code_tools = CodeTools(verbose=True)
        
        # Test Python code analysis
        sample_code = '''
def hello_world():
    """A simple hello world function."""
    print("Hello, World!")
    return "success"

class TestClass:
    """A test class."""
    
    def __init__(self):
        self.value = 42
    
    def get_value(self):
        return self.value
'''
        
        result = await code_tools.analyze_python_code(sample_code)
        if result.success:
            data = result.data
            print(f"✓ Code analysis successful:")
            print(f"  - Functions: {len(data['functions'])}")
            print(f"  - Classes: {len(data['classes'])}")
            print(f"  - Lines of code: {data['metrics']['lines_of_code']}")
        else:
            print(f"✗ Code analysis failed: {result.error}")
            return False
        
        # Test syntax validation
        result = await code_tools.validate_syntax(sample_code, "python")
        if result.success and result.data['valid']:
            print("✓ Syntax validation successful")
        else:
            print(f"✗ Syntax validation failed: {result.error}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Code analysis error: {e}")
        return False


async def test_system_info():
    """Test system information gathering."""
    print("\n💻 Testing System Information")
    
    try:
        system_tools = SystemTools(verbose=True)
        
        # Test system info
        result = await system_tools.get_system_info()
        if result.success:
            data = result.data
            print(f"✓ System info retrieved:")
            print(f"  - Platform: {data['platform']['system']}")
            print(f"  - CPU cores: {data['cpu']['logical_cores']}")
            print(f"  - Memory: {data['memory']['total_formatted']}")
        else:
            print(f"✗ System info failed: {result.error}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ System info error: {e}")
        return False


def test_configuration():
    """Test configuration system."""
    print("\n⚙️ Testing Configuration")
    
    try:
        settings = get_settings()
        
        # Test basic settings
        print(f"✓ App name: {settings.app_name}")
        print(f"✓ Data directory: {settings.data_dir}")
        print(f"✓ Tools enabled: {settings.tools_enabled}")
        
        # Test provider configs
        try:
            deepseek_config = settings.get_provider_config("deepseek")
            print(f"✓ DeepSeek config available")
        except Exception as e:
            print(f"⚠ DeepSeek config issue: {e}")
        
        try:
            ollama_config = settings.get_provider_config("ollama")
            print(f"✓ Ollama config available")
        except Exception as e:
            print(f"⚠ Ollama config issue: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration error: {e}")
        return False


async def main():
    """Run all tests."""
    print("🚀 Arien AI Installation Test")
    print("=" * 50)
    
    tests = [
        ("Basic Functionality", test_basic_functionality()),
        ("File Operations", test_file_operations()),
        ("Code Analysis", test_code_analysis()),
        ("System Information", test_system_info()),
        ("Configuration", test_configuration())
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_coro in tests:
        try:
            if asyncio.iscoroutine(test_coro):
                result = await test_coro
            else:
                result = test_coro
            
            if result:
                passed += 1
                print(f"\n✅ {test_name}: PASSED")
            else:
                print(f"\n❌ {test_name}: FAILED")
                
        except Exception as e:
            print(f"\n💥 {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Arien AI is ready to use.")
        print("\nNext steps:")
        print("1. Set up your LLM provider API keys")
        print("2. Run: arien chat --help")
        print("3. Start chatting: arien chat")
        return True
    else:
        print("⚠️ Some tests failed. Please check the errors above.")
        return False


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 Unexpected error: {e}")
        sys.exit(1)
