"""
LLM Provider Implementations

Support for DeepSeek and Ollama providers with function calling capabilities.
"""

import asyncio
import json
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union

import httpx
from openai import OpenAI
import ollama

from ..config.settings import get_settings
from ..utils.logger import get_logger


class BaseLLMProvider(ABC):
    """Base class for all LLM providers."""
    
    def __init__(self, model: str, verbose: bool = False):
        self.model = model
        self.verbose = verbose
        self.logger = get_logger(__name__)
        self.settings = get_settings()
    
    @abstractmethod
    async def generate_response(
        self,
        messages: List[Dict[str, Any]],
        tools: Optional[List[Dict[str, Any]]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate a response from the LLM."""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """Check if the provider is available and configured."""
        pass

    @abstractmethod
    def list_models(self) -> List[str]:
        """List available models for this provider."""
        pass


class DeepSeekProvider(BaseLLMProvider):
    """
    DeepSeek API provider using OpenAI-compatible interface.
    
    Supports both deepseek-chat and deepseek-reasoner models.
    """
    
    def __init__(self, model: str = "deepseek-chat", verbose: bool = False):
        super().__init__(model, verbose)
        
        # Initialize OpenAI client with DeepSeek configuration
        self.client = OpenAI(
            api_key=self.settings.deepseek_api_key,
            base_url="https://api.deepseek.com"
        )
        
        # Model-specific configurations
        self.model_configs = {
            "deepseek-chat": {
                "supports_tools": True,
                "max_tokens": 4096,
                "temperature": 0.7
            },
            "deepseek-reasoner": {
                "supports_tools": True,
                "max_tokens": 4096,
                "temperature": 0.7,
                "reasoning_enabled": True
            }
        }
        
        self.config = self.model_configs.get(model, self.model_configs["deepseek-chat"])
        
        if self.verbose:
            self.logger.info(f"DeepSeek provider initialized with model: {model}")
    
    async def generate_response(
        self,
        messages: List[Dict[str, Any]],
        tools: Optional[List[Dict[str, Any]]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate response using DeepSeek API."""
        try:
            # Prepare request parameters
            request_params = {
                "model": self.model,
                "messages": messages,
                "max_tokens": kwargs.get("max_tokens", self.config["max_tokens"]),
                "temperature": kwargs.get("temperature", self.config["temperature"]),
                "stream": False
            }
            
            # Add tools if supported and provided
            if tools and self.config["supports_tools"]:
                request_params["tools"] = tools
                request_params["tool_choice"] = "auto"
            
            if self.verbose:
                self.logger.info(f"Sending request to DeepSeek with {len(messages)} messages")
                if tools:
                    self.logger.info(f"Available tools: {len(tools)}")
            
            # Make API call
            response = await asyncio.to_thread(
                self.client.chat.completions.create,
                **request_params
            )
            
            # Process response
            choice = response.choices[0]
            result = {
                "content": choice.message.content,
                "role": "assistant"
            }
            
            # Handle tool calls
            if hasattr(choice.message, 'tool_calls') and choice.message.tool_calls:
                result["tool_calls"] = []
                for tool_call in choice.message.tool_calls:
                    result["tool_calls"].append({
                        "id": tool_call.id,
                        "type": tool_call.type,
                        "function": {
                            "name": tool_call.function.name,
                            "arguments": tool_call.function.arguments
                        }
                    })
            
            # Add usage information
            if hasattr(response, 'usage'):
                result["usage"] = {
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                }
            
            if self.verbose:
                self.logger.info(f"Received response from DeepSeek: {len(result.get('content', ''))} characters")
                if result.get("tool_calls"):
                    self.logger.info(f"Tool calls requested: {len(result['tool_calls'])}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"DeepSeek API error: {e}")
            raise
    
    def is_available(self) -> bool:
        """Check if DeepSeek is available."""
        try:
            if not self.settings.deepseek_api_key:
                return False

            # Test API connection
            test_response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": "test"}],
                max_tokens=1
            )
            return True

        except Exception as e:
            self.logger.warning(f"DeepSeek not available: {e}")
            return False

    def list_models(self) -> List[str]:
        """List available DeepSeek models."""
        return list(self.model_configs.keys())

    def get_model_info(self, model_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific model."""
        return self.model_configs.get(model_name)


class OllamaProvider(BaseLLMProvider):
    """
    Ollama provider for local LLM inference.
    
    Supports any model available in Ollama with function calling capabilities.
    """
    
    def __init__(self, model: str = "llama3.2", verbose: bool = False):
        super().__init__(model, verbose)
        
        # Initialize Ollama client
        self.client = ollama.Client(
            host=self.settings.ollama_host or "http://localhost:11434"
        )
        
        # Default configuration
        self.config = {
            "supports_tools": True,
            "temperature": 0.7,
            "num_predict": 4096
        }
        
        if self.verbose:
            self.logger.info(f"Ollama provider initialized with model: {model}")
    
    async def generate_response(
        self,
        messages: List[Dict[str, Any]],
        tools: Optional[List[Dict[str, Any]]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate response using Ollama."""
        try:
            # Prepare request parameters
            request_params = {
                "model": self.model,
                "messages": messages,
                "stream": False,
                "options": {
                    "temperature": kwargs.get("temperature", self.config["temperature"]),
                    "num_predict": kwargs.get("max_tokens", self.config["num_predict"])
                }
            }
            
            # Add tools if provided
            if tools and self.config["supports_tools"]:
                request_params["tools"] = tools
            
            if self.verbose:
                self.logger.info(f"Sending request to Ollama with {len(messages)} messages")
                if tools:
                    self.logger.info(f"Available tools: {len(tools)}")
            
            # Make API call
            response = await asyncio.to_thread(
                self.client.chat,
                **request_params
            )
            
            # Process response
            result = {
                "content": response["message"]["content"],
                "role": "assistant"
            }
            
            # Handle tool calls
            if "tool_calls" in response["message"]:
                result["tool_calls"] = response["message"]["tool_calls"]
            
            # Add usage information if available
            if "eval_count" in response:
                result["usage"] = {
                    "prompt_tokens": response.get("prompt_eval_count", 0),
                    "completion_tokens": response.get("eval_count", 0),
                    "total_tokens": response.get("prompt_eval_count", 0) + response.get("eval_count", 0)
                }
            
            if self.verbose:
                self.logger.info(f"Received response from Ollama: {len(result.get('content', ''))} characters")
                if result.get("tool_calls"):
                    self.logger.info(f"Tool calls requested: {len(result['tool_calls'])}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Ollama API error: {e}")
            raise
    
    def is_available(self) -> bool:
        """Check if Ollama is available."""
        try:
            # Test connection
            models = self.client.list()
            
            # Check if the specified model is available
            available_models = [model["name"] for model in models["models"]]
            if self.model not in available_models:
                self.logger.warning(f"Model {self.model} not found in Ollama. Available: {available_models}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.warning(f"Ollama not available: {e}")
            return False
    
    async def pull_model(self, model_name: Optional[str] = None) -> bool:
        """Pull a model from Ollama registry."""
        model_to_pull = model_name or self.model
        
        try:
            self.logger.info(f"Pulling model: {model_to_pull}")
            await asyncio.to_thread(self.client.pull, model_to_pull)
            self.logger.info(f"Successfully pulled model: {model_to_pull}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to pull model {model_to_pull}: {e}")
            return False
    
    def list_models(self) -> List[str]:
        """List available models in Ollama."""
        try:
            models = self.client.list()
            return [model["name"] for model in models["models"]]
        except Exception as e:
            self.logger.error(f"Failed to list models: {e}")
            return []
