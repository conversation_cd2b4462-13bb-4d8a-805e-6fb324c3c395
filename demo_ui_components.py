#!/usr/bin/env python3
"""
Demo script to showcase the new UI components
"""

import time
import os
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.table import Table

def demo_banner():
    """Demo the banner display."""
    console = Console()
    
    banner_text = Text()
    banner_text.append("🤖 ", style="bold blue")
    banner_text.append("ARIEN AI", style="bold cyan")
    banner_text.append(" - Enhanced CLI Terminal UI", style="bold white")
    
    panel = Panel(
        banner_text,
        border_style="cyan",
        padding=(1, 2),
        title="[bold blue]Welcome to Enhanced UI[/bold blue]",
        title_align="center",
    )
    console.print(panel)
    console.print()

def demo_spinner_animation():
    """Demo the custom spinner animation."""
    console = Console()
    console.print("[bold cyan]🎯 Custom Spinner Animation Demo[/bold cyan]")
    console.print()
    
    ball_frames = [
        "( ●    )",
        "(  ●   )",
        "(   ●  )",
        "(    ● )",
        "(     ●)",
        "(    ● )",
        "(   ●  )",
        "(  ●   )",
        "( ●    )",
        "(●     )",
    ]
    
    console.print("[dim]Simulating spinner animation with elapsed time...[/dim]")
    console.print()
    
    start_time = time.time()
    for cycle in range(3):  # 3 full cycles
        for frame_idx, frame in enumerate(ball_frames):
            elapsed = time.time() - start_time
            
            if elapsed < 60:
                time_str = f"{elapsed:.1f}s"
            else:
                minutes = int(elapsed // 60)
                seconds = elapsed % 60
                time_str = f"{minutes}m {seconds:.1f}s"
            
            # Create animated line
            line = f"\r[cyan]{frame}[/cyan] [bold green]Processing AI request[/bold green] [dim]({time_str})[/dim]"
            console.print(line, end="")
            time.sleep(0.2)
    
    console.print("\n[bold green]✅ Processing completed![/bold green]")
    console.print()

def demo_slash_commands():
    """Demo the slash commands interface."""
    console = Console()
    console.print("[bold cyan]⚡ Slash Commands System Demo[/bold cyan]")
    console.print()
    
    console.print("[dim]Available slash commands when you type '/':[/dim]")
    
    commands_table = Table(show_header=True, header_style="bold magenta", box=None)
    commands_table.add_column("Command", style="cyan", width=15)
    commands_table.add_column("Description", style="white")
    commands_table.add_column("Example", style="dim")
    
    commands = [
        ("/model", "Switch LLM model", "/model deepseek-chat"),
        ("/provider", "Switch LLM provider", "/provider ollama"),
        ("/clear", "Clear chat history", "/clear"),
        ("/status", "Show system status", "/status"),
        ("/config", "Show/modify config", "/config show"),
        ("/help", "Show available commands", "/help"),
        ("/exit", "Exit the application", "/exit"),
    ]
    
    for cmd, desc, example in commands:
        commands_table.add_row(cmd, desc, example)
    
    console.print(commands_table)
    console.print()
    
    # Demo model selection
    console.print("[bold yellow]Example: Model Selection Interface[/bold yellow]")
    
    model_table = Table(title="Available DeepSeek Models", show_header=True, header_style="bold magenta", box=None)
    model_table.add_column("Option", style="cyan", width=8)
    model_table.add_column("Model", style="white")
    
    models = ["deepseek-chat", "deepseek-reasoner"]
    for i, model in enumerate(models, 1):
        model_table.add_row(str(i), model)
    
    console.print(model_table)
    console.print("[dim]User would select: 1 for deepseek-chat[/dim]")
    console.print()

def demo_login_interface():
    """Demo the login interface."""
    console = Console()
    console.print("[bold cyan]🔐 Login Component Demo[/bold cyan]")
    console.print()
    
    # Login banner
    banner_text = Text()
    banner_text.append("🔐 ", style="bold yellow")
    banner_text.append("LLM Provider Configuration", style="bold cyan")
    
    panel = Panel(
        banner_text,
        border_style="yellow",
        padding=(1, 2),
        title="[bold yellow]System Activation[/bold yellow]",
        title_align="center",
    )
    console.print(panel)
    console.print()
    
    # Provider selection
    console.print("[bold cyan]Available LLM Providers:[/bold cyan]")
    
    provider_table = Table(show_header=True, header_style="bold magenta", box=None)
    provider_table.add_column("Option", style="cyan")
    provider_table.add_column("Provider", style="white")
    provider_table.add_column("Description", style="dim")
    
    providers = [
        ("1", "DeepSeek", "API Key Required"),
        ("2", "Ollama", "Local/Self-hosted"),
    ]
    
    for option, provider, desc in providers:
        provider_table.add_row(option, provider, desc)
    
    console.print(provider_table)
    console.print()
    
    # Success message
    success_text = Text()
    success_text.append("✅ ", style="bold green")
    success_text.append("Configuration Complete!", style="bold green")
    
    details = Text()
    details.append("Provider: DeepSeek\n", style="cyan")
    details.append("Model: deepseek-chat\n", style="white")
    
    panel = Panel(
        details,
        title=success_text,
        border_style="green",
        padding=(1, 2),
    )
    console.print(panel)
    console.print()

def demo_enhanced_help():
    """Demo the enhanced help interface."""
    console = Console()
    console.print("[bold cyan]📚 Enhanced Help System Demo[/bold cyan]")
    console.print()
    
    help_table = Table(title="Available Commands", show_header=True, header_style="bold magenta")
    help_table.add_column("Command", style="cyan", width=15)
    help_table.add_column("Description", style="white")
    help_table.add_column("Usage", style="dim")
    
    # Basic commands
    help_table.add_row("help", "Show this help message", "help")
    help_table.add_row("clear", "Clear the screen", "clear")
    help_table.add_row("status", "Show agent status", "status")
    help_table.add_row("exit/quit/bye", "Exit the chat session", "exit")
    
    # Slash commands
    help_table.add_row("/model", "Switch LLM model", "/model [model_name]")
    help_table.add_row("/provider", "Switch LLM provider", "/provider [provider_name]")
    help_table.add_row("/clear", "Clear chat history", "/clear")
    help_table.add_row("/status", "Show system status", "/status")
    help_table.add_row("/config", "Show/modify config", "/config [key] [value]")
    help_table.add_row("/help", "Show slash commands", "/help")
    
    # Special features
    help_table.add_row("Double ESC", "Interrupt processing", "Press ESC twice quickly")
    help_table.add_row("Ctrl+C", "Interrupt/Exit", "Press Ctrl+C")
    
    console.print(help_table)
    console.print()

def demo_status_display():
    """Demo the status display."""
    console = Console()
    console.print("[bold cyan]📊 System Status Demo[/bold cyan]")
    console.print()
    
    status_table = Table(title="Agent Status", show_header=True, header_style="bold magenta")
    status_table.add_column("Metric", style="cyan")
    status_table.add_column("Value", style="green")
    
    status_data = [
        ("Provider", "DeepSeekProvider"),
        ("Model", "deepseek-chat"),
        ("Uptime", "0:05:23"),
        ("Total Messages", "12"),
        ("Conversation Length", "24"),
        ("Successful Tool Calls", "8"),
        ("Failed Tool Calls", "1"),
        ("Tool Success Rate", "88.9%"),
        ("Available Tools", "15"),
        ("Cached Results", "3"),
    ]
    
    for metric, value in status_data:
        status_table.add_row(metric, value)
    
    console.print(status_table)
    console.print()

def main():
    """Run the complete UI demo."""
    console = Console()
    
    # Clear screen and show title
    console.clear()
    console.print("[bold blue]🎨 Arien AI Enhanced CLI Terminal UI Demo[/bold blue]")
    console.print("[dim]Showcasing the new interactive components and features[/dim]")
    console.print()
    
    # Demo each component
    demo_banner()
    input("[dim]Press Enter to continue to spinner demo...[/dim]")
    
    demo_spinner_animation()
    input("[dim]Press Enter to continue to login demo...[/dim]")
    
    demo_login_interface()
    input("[dim]Press Enter to continue to slash commands demo...[/dim]")
    
    demo_slash_commands()
    input("[dim]Press Enter to continue to help demo...[/dim]")
    
    demo_enhanced_help()
    input("[dim]Press Enter to continue to status demo...[/dim]")
    
    demo_status_display()
    
    # Final message
    console.print("[bold green]🎉 Demo completed! The enhanced UI provides:[/bold green]")
    console.print("  ✅ Interactive login and configuration")
    console.print("  ✅ Real-time spinner with elapsed time")
    console.print("  ✅ Slash commands with dropdown selection")
    console.print("  ✅ Enhanced help and status displays")
    console.print("  ✅ Double ESC interrupt functionality")
    console.print("  ✅ Modular, extensible component architecture")
    console.print()
    console.print("[bold cyan]Ready to enhance your AI CLI experience! 🚀[/bold cyan]")

if __name__ == "__main__":
    main()
